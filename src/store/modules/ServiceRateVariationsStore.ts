import { refreshAllClientRateVariationsExpirationSummaries } from '@/helpers/ClientExpirationSummaryHelpers/ClientExpirationSummaryHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { ClientServiceRateVariationsRequest } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsRequest';
import { ClientServiceRateVariationsResponse } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsResponse';
import { ClientServiceRateVariationsStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsStatus';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';

// TODO: This store has no state, getters, or actions that require reactivity.
// It could be converted to a simple module or utility function instead of a Pinia store.
export const useServiceRateVariationsStore = defineStore(
  'serviceRateVariationsStore',
  () => {
    /**
     * Saves a full ClientServiceRateVariations document over websocket and listens for the
     * response. Returns the saved ClientServiceRate if successful, otherwise null.
     * @param rateVariations - The ClientServiceRate to save
     * @returns ClientServiceRate | null
     */
    async function saveClientServiceRateVariations(
      rateVariations: ClientServiceRateVariations,
    ): Promise<ClientServiceRateVariations | null> {
      try {
        // Set company and division if not already set
        rateVariations.company ||= sessionManager.getCompanyId();
        rateVariations.division ||= sessionManager.getDivisionId();

        const result: ClientServiceRateVariations | string | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/save',
              rateVariations,
              true,
            ),
            'savedClientServiceRateVariations',
          );
        // If result is a string, it indicates an error message
        if (typeof result === 'string') {
          throw new Error(result);
        }

        // Update client expiration summaries when rate variation is saved
        if (result) {
          // Load all current variations to update summaries
          const allVariations = await getDivisionRateVariationsByStatus(
            ClientServiceRateVariationsStatus.ALL,
          );
          if (allVariations) {
            refreshAllClientRateVariationsExpirationSummaries(allVariations);
          }
        }

        return result;
      } catch (error) {
        logConsoleError('Error saving Client Service Rate Variations', error);
        return null;
      }
    }

    /**
     * Saves or updates a list of ClientServiceRateVariations documents over
     * websocket and listens for the response. Returns the saved
     * ClientServiceRateVariations[] if successful, otherwise null.
     * @param rateVariationsList - The list of ClientServiceRateVariations to
     * save
     * @returns ClientServiceRateVariations[] | null
     */
    async function saveAllClientServiceRateVariations(
      rateVariationsList: ClientServiceRateVariations[],
    ): Promise<ClientServiceRateVariations[] | null> {
      try {
        // Set company and division for each item if not already set
        const companyId = sessionManager.getCompanyId();
        const divisionId = sessionManager.getDivisionId();
        rateVariationsList.forEach((item) => {
          item.company ||= companyId;
          item.division ||= divisionId;
        });

        const result: ClientServiceRateVariations[] | string | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/saveAll',
              rateVariationsList,
              true,
            ),
            'savedClientServiceRateVariationsList',
          );
        // If result is a string, it indicates an error message
        if (typeof result === 'string') {
          throw new Error(result);
        }

        // Update client expiration summaries when rate variations are saved
        if (result && result.length > 0) {
          // Load all current variations to update summaries
          const allVariations = await getDivisionRateVariationsByStatus(
            ClientServiceRateVariationsStatus.ALL,
          );
          if (allVariations) {
            refreshAllClientRateVariationsExpirationSummaries(allVariations);
          }
        }

        return result;
      } catch (error) {
        logConsoleError(
          'Error saving all Client Service Rate Variations',
          error,
        );
        return null;
      }
    }

    /**
     * Requests the service rate variations for a specific client and date.
     * @param clientId - The ID of the client for which to fetch service rate variations
     * @param searchDate - The date in epoch time for which to fetch service rate variations
     * @returns ClientServiceRateVariations[] | null
     */
    async function getServiceRateVariationsByClient(
      clientId: string | undefined,
      searchDate: number,
    ): Promise<ClientServiceRateVariations[] | null> {
      try {
        if (!clientId) {
          throw new Error(
            'Client ID is required to fetch Service Rate Variations',
          );
        }

        // Conditionally set the endpoint based on whether the user is in the
        // client portal
        const endpointPath =
          '/clientServiceRateVariations/getServiceRateVariationsByClient';
        const endpoint = sessionManager.isClientPortal()
          ? `/user/${sessionManager.getUserName()}${endpointPath}`
          : endpointPath;

        // Construct the request payload
        const request: ClientServiceRateVariationsRequest = {
          clientId,
          searchDate,
        };
        const result: ClientServiceRateVariationsResponse | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(endpoint, request, true),
            'serviceRateVariationsByClient',
            {
              mapResponse: (
                response: ClientServiceRateVariationsResponse | null,
              ) => {
                // TODO: Do we need to also handle the response clientId '0'?
                return !response || response.clientId === request.clientId;
              },
            },
          );
        // // TODO: Debugging only - remove in production
        // if (result?.clientServiceRateVariations) {
        //   console.table(
        //     result.clientServiceRateVariations.map((x) => ({
        //       rateTypeName: returnRateTypeLongNameFromId(x.rateTypeId),
        //       serviceTypeName: returnServiceTypeLongNameFromId(x.serviceTypeId),
        //       ...x,
        //     })),
        //   );
        // }
        const clientVariations = result?.clientServiceRateVariations ?? null;

        // Update client expiration summaries when rate variations are loaded for a specific client
        if (clientVariations && clientVariations.length > 0) {
          // Load all current variations to update summaries comprehensively
          const allVariations = await getDivisionRateVariationsByStatus(
            ClientServiceRateVariationsStatus.ALL,
          );
          if (allVariations) {
            refreshAllClientRateVariationsExpirationSummaries(allVariations);
          }
        }

        return clientVariations;
      } catch (error) {
        logConsoleError(
          'Error fetching Service Rate Variations by Client',
          error,
        );
        return null;
      }
    }

    /**
     * Requests the service rate variations for the division of the current user, based on the provided status
     */
    async function getDivisionRateVariationsByStatus(
      status: ClientServiceRateVariationsStatus,
    ) {
      try {
        if (!status) {
          throw new Error(
            'Status is required to fetch Division Rate Variations',
          );
        }
        const result: ClientServiceRateVariations[] | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/getClientServiceRateVariationsByDivision',
              status,
              true,
            ),
            'serviceRateVariationsByDivision',
          );

        // Update client expiration summaries when rate variations are loaded
        if (result && result.length > 0) {
          refreshAllClientRateVariationsExpirationSummaries(result);
        }

        return result;
      } catch (error) {
        logConsoleError(
          'Error fetching Division Rate Variations by Status',
          error,
        );
        return null;
      }
    }

    return {
      saveClientServiceRateVariations,
      saveAllClientServiceRateVariations,
      getServiceRateVariationsByClient,
      getDivisionRateVariationsByStatus,
    };
  },
);
