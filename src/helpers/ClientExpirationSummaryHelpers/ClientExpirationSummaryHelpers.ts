import { generateServiceRateVariationsSummary } from '@/helpers/RateVariationsExpirationHelpers/RateVariationsExpirationHelpers';
import { ClientRateExpirationSummaries } from '@/interface-models/Client/ClientDetails/ClientRateExpirationSummary';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';

/**
 * Updates a client's rate expiration summaries with the latest rate variations data
 * @param clientId - The client ID to update
 * @param allRateVariations - Array of all rate variations to check against
 * @returns Updated ClientRateExpirationSummaries or null if client not found
 */
export function updateClientRateVariationsExpirationSummary(
  clientId: string,
  allRateVariations: ClientServiceRateVariations[],
): ClientRateExpirationSummaries | null {
  const clientDetailsStore = useClientDetailsStore();

  // Find the client in the store
  const client = clientDetailsStore.clientSummaryList.find(
    (c) => c.clientId === clientId,
  );

  if (!client) {
    return null;
  }

  // Get current expiration summaries or create new ones
  const currentSummaries =
    client.rateExpirationSummaries || new ClientRateExpirationSummaries();

  // Generate the new rate variations summary
  const rateVariationsSummary = generateServiceRateVariationsSummary(
    allRateVariations,
    clientId,
  );

  // Update the summaries with the new rate variations data
  const updatedSummaries = new ClientRateExpirationSummaries(
    currentSummaries.serviceRateSummary,
    currentSummaries.fuelSurchargeSummary,
    currentSummaries.defaultsConfigurationSummary,
    rateVariationsSummary,
  );

  return updatedSummaries;
}

/**
 * Updates multiple clients' rate expiration summaries with rate variations data
 * @param clientIds - Array of client IDs to update
 * @param allRateVariations - Array of all rate variations to check against
 * @returns Map of clientId to updated ClientRateExpirationSummaries
 */
export function updateMultipleClientsRateVariationsExpirationSummaries(
  clientIds: string[],
  allRateVariations: ClientServiceRateVariations[],
): Map<string, ClientRateExpirationSummaries> {
  const updatedSummaries = new Map<string, ClientRateExpirationSummaries>();

  clientIds.forEach((clientId) => {
    const summary = updateClientRateVariationsExpirationSummary(
      clientId,
      allRateVariations,
    );
    if (summary) {
      updatedSummaries.set(clientId, summary);
    }
  });

  return updatedSummaries;
}

/**
 * Gets all client IDs that are affected by the given rate variations
 * @param rateVariations - Array of rate variations to check
 * @returns Array of unique client IDs that are affected
 */
export function getAffectedClientIds(
  rateVariations: ClientServiceRateVariations[],
): string[] {
  const clientIds = new Set<string>();

  rateVariations.forEach((variation) => {
    variation.applyToIds.forEach((clientId) => {
      clientIds.add(clientId);
    });
  });

  return Array.from(clientIds);
}

/**
 * Updates all affected clients' expiration summaries when rate variations change
 * This function should be called whenever rate variations are added, updated, or deleted
 * @param allRateVariations - Array of all current rate variations
 */
export function refreshAllClientRateVariationsExpirationSummaries(
  allRateVariations: ClientServiceRateVariations[],
): void {
  const affectedClientIds = getAffectedClientIds(allRateVariations);
  const clientDetailsStore = useClientDetailsStore();

  affectedClientIds.forEach((clientId) => {
    const updatedSummaries = updateClientRateVariationsExpirationSummary(
      clientId,
      allRateVariations,
    );

    if (updatedSummaries) {
      // Find and update the client in the store
      const client = clientDetailsStore.clientSummaryList.find(
        (c) => c.clientId === clientId,
      );

      if (client) {
        client.rateExpirationSummaries = updatedSummaries;
      }
    }
  });
}
