import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import {
  ExpiringDocumentType,
  RateExpirationSummary,
} from '@/interface-models/ServiceRates/RateExpirationSummary';

/**
 * Generates RateExpirationSummary objects from ClientServiceRateVariations for a specific client
 * @param rateVariations - Array of rate variations to process
 * @param clientId - The client ID to filter variations for
 * @returns Array of RateExpirationSummary objects for expired or expiring variations
 */
export function generateRateVariationsExpirationSummary(
  rateVariations: ClientServiceRateVariations[],
  clientId: string,
): RateExpirationSummary[] {
  if (!rateVariations || rateVariations.length === 0) {
    return [];
  }

  const now = new Date().getTime();
  const sevenDaysFromNow = now + 7 * 24 * 60 * 60 * 1000;

  // Filter variations that apply to this client
  const clientVariations = rateVariations.filter((variation) =>
    variation.applyToIds.includes(clientId),
  );

  // Process each variation to create expiration summaries
  const expirationSummaries: RateExpirationSummary[] = [];

  clientVariations.forEach((variation) => {
    const validFrom = variation.validFromDate || 0;
    const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;

    // Only include variations that are expired or expiring soon
    const isExpired = now > validTo;
    const isExpiringSoon = !isExpired && validTo < sevenDaysFromNow;

    if (isExpired || isExpiringSoon) {
      // Generate a descriptive name for the variation
      const serviceTypeName = variation.serviceTypeId
        ? returnServiceTypeLongNameFromId(variation.serviceTypeId)
        : 'All Service Types';

      const rateTypeName = variation.rateTypeId
        ? returnRateTypeLongNameFromId(variation.rateTypeId)
        : 'All Rate Types';

      // Create adjustment description
      const adjustments: string[] = [];
      if (variation.clientAdjustmentPercentage !== null) {
        adjustments.push(
          `Client: ${variation.clientAdjustmentPercentage > 0 ? '+' : ''}${
            variation.clientAdjustmentPercentage
          }%`,
        );
      }
      if (variation.fleetAssetAdjustmentPercentage !== null) {
        adjustments.push(
          `Fleet: ${variation.fleetAssetAdjustmentPercentage > 0 ? '+' : ''}${
            variation.fleetAssetAdjustmentPercentage
          }%`,
        );
      }

      const adjustmentText =
        adjustments.length > 0 ? ` (${adjustments.join(', ')})` : '';
      const name = `Rate Variation: ${serviceTypeName} - ${rateTypeName}${adjustmentText}`;

      expirationSummaries.push({
        attentionRequired: true,
        name,
        validFromDate: variation.validFromDate || undefined,
        validToDate: variation.validToDate || undefined,
        priority: isExpired ? 1 : isExpiringSoon ? 2 : 3,
        entityId: clientId,
        expirationType: ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
      });
    }
  });

  // Sort by priority (urgent first), then by expiration date
  return expirationSummaries.sort((a, b) => {
    if (a.priority !== b.priority) {
      return (a.priority || 3) - (b.priority || 3);
    }
    // Sort by expiration date (earliest first)
    const aDate = a.validToDate || 0;
    const bDate = b.validToDate || 0;
    return aDate - bDate;
  });
}

/**
 * Checks if a client has any expired or expiring rate variations
 * @param rateVariations - Array of rate variations to check
 * @param clientId - The client ID to check for
 * @returns True if the client has expired or expiring rate variations
 */
export function hasExpiredOrExpiringRateVariations(
  rateVariations: ClientServiceRateVariations[],
  clientId: string,
): boolean {
  const summaries = generateRateVariationsExpirationSummary(
    rateVariations,
    clientId,
  );
  return summaries.length > 0;
}

/**
 * Gets the most urgent rate variation expiration for a client
 * @param rateVariations - Array of rate variations to check
 * @param clientId - The client ID to check for
 * @returns The most urgent RateExpirationSummary or null if none found
 */
export function getMostUrgentRateVariationExpiration(
  rateVariations: ClientServiceRateVariations[],
  clientId: string,
): RateExpirationSummary | null {
  const summaries = generateRateVariationsExpirationSummary(
    rateVariations,
    clientId,
  );
  return summaries.length > 0 ? summaries[0] : null;
}

/**
 * Generates a single RateExpirationSummary for the serviceRateVariationsSummary field
 * This represents the most urgent rate variation expiration for a client
 * @param rateVariations - Array of rate variations to process
 * @param clientId - The client ID to generate summary for
 * @returns RateExpirationSummary for the most urgent expiration or null if none found
 */
export function generateServiceRateVariationsSummary(
  rateVariations: ClientServiceRateVariations[],
  clientId: string,
): RateExpirationSummary | null {
  const summaries = generateRateVariationsExpirationSummary(
    rateVariations,
    clientId,
  );

  if (summaries.length === 0) {
    return null;
  }

  // If there are multiple expired/expiring variations, create a summary that represents them all
  const mostUrgent = summaries[0];
  const expiredCount = summaries.filter((s) => s.priority === 1).length;
  const expiringSoonCount = summaries.filter((s) => s.priority === 2).length;

  let summaryName = '';
  if (expiredCount > 0 && expiringSoonCount > 0) {
    summaryName = `Rate Variations: ${expiredCount} expired, ${expiringSoonCount} expiring soon`;
  } else if (expiredCount > 0) {
    summaryName =
      expiredCount === 1
        ? 'Rate Variation: 1 expired'
        : `Rate Variations: ${expiredCount} expired`;
  } else if (expiringSoonCount > 0) {
    summaryName =
      expiringSoonCount === 1
        ? 'Rate Variation: 1 expiring soon'
        : `Rate Variations: ${expiringSoonCount} expiring soon`;
  }

  return {
    attentionRequired: true,
    name: summaryName,
    validFromDate: mostUrgent.validFromDate,
    validToDate: mostUrgent.validToDate,
    priority: mostUrgent.priority,
    entityId: clientId,
    expirationType: ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
  };
}
