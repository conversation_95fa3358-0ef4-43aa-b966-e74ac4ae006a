import DashboardActiveJobCounter from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/active_job_counter/active_job_counter.vue';
import ActiveTrucksWithNoDriverCounter from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/active_trucks_with_no_driver_counter/active_trucks_with_no_driver_counter.vue';
import ClientsRequiringAttentionCounter from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/clients_requiring_attention_counter/clients_requiring_attention_counter.vue';
import DisassociatedDriverCounter from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/dissociated_driver_counter/dissociated_driver_counter.vue';
import LastBookingDateCounter from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/last_booking_date_counter/last_booking_date_counter.vue';
import SubcontractorSummaryDetails from '@/components/admin/SubcontractorIndex/components/administration_dashboard/components/subcontractor_summary_details/subcontractor_summary_details.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  DashboardJobTableData,
  DashboardTableData,
  combineExpirationSummaries,
} from '@/helpers/ExpirationSummaryHelpers/ExpirationSummaryHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { InvoiceEntityCategory } from '@/interface-models/InvoiceAdjustment/EntityTypes/InvoiceEntityCategory';
import { ClientServiceRateVariationsStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsStatus';
import {
  ExpiringDocumentType,
  RateExpirationSummaryGroup,
} from '@/interface-models/ServiceRates/RateExpirationSummary';
import {
  AdminDashboardNavigationState,
  useAppNavigationStore,
} from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface TabItem {
  id: string;
  name: string;
  type?: EntityType;
}
interface ExpirationCount {
  total: number;
  urgent: number;
  soon: number;
}
interface ExpirationType {
  id: string;
  longName: string;
  description: string;
  icon: string;
  showEntityTypes: EntityType[];
  expirationType?: ExpiringDocumentType;
  isVisible: boolean;
  count: ExpirationCount;
  tooltip: string;
}

interface ActivityItemType {
  id: string;
  longName: string;
  description: string;
  icon: string;
  entityType?: EntityType;
}

enum MainContentViewType {
  EXPIRATIONS = 'EXPIRATIONS',
  ACTIVITY = 'ACTIVITY',
}

@Component({
  components: {
    LastBookingDateCounter,
    DashboardActiveJobCounter,
    DisassociatedDriverCounter,
    ActiveTrucksWithNoDriverCounter,
    SubcontractorSummaryDetails,
    ClientsRequiringAttentionCounter,
    InformationTooltip,
  },
})
export default class AdministrationDashboard extends Vue {
  @Prop() public category: InvoiceEntityCategory;

  public fleetAssetStore = useFleetAssetStore();
  public appNavigationStore = useAppNavigationStore();
  public clientDetailsStore = useClientDetailsStore();
  public fleetAssetOwnerStore = useFleetAssetOwnerStore();
  public serviceRateVariationsStore = useServiceRateVariationsStore();

  public fleetAssetExpirationSummaries: RateExpirationSummaryGroup[] = [];
  public fleetAssetOwnerExpirationSummaries: RateExpirationSummaryGroup[] = [];
  public driverExpirationSummaries: RateExpirationSummaryGroup[] = [];
  public clientExpirationSummaries: RateExpirationSummaryGroup[] = [];

  public activityTableData: DashboardTableData[] = [];
  public activityJobTableData: DashboardJobTableData[] = [];

  public selectedPriorityOption: number = 1;

  public returnFormattedDate = returnFormattedDate;

  public tabItems: TabItem[] = [];

  get entityActivityList(): ActivityItemType[] {
    return this.category === InvoiceEntityCategory.SUBCONTRACTOR
      ? [
          {
            id: 'ACTIVE_JOBS',
            longName: 'Active Jobs',
            description: 'Drivers and vehicles assigned to in-progress jobs.',
            icon: 'fal fa-truck-loading',
          },
          {
            id: 'DISSOCIATED_DRIVERS',
            longName: 'Dissociated Drivers',
            description:
              'Drivers that have no association with a Fleet Asset Owner.',
            icon: 'fal fa-user-slash',
          },
          {
            id: 'TRUCKS_WITH_NO_DRIVERS',
            longName: 'Trucks with No Drivers',
            description: 'Active trucks that do not have an associated driver.',
            icon: 'fal fa-truck',
          },

          {
            id: 'LOW_ENGAGEMENT_DRIVERS',
            longName: 'Low-engagement Drivers',
            description:
              'Drivers who have not been marked as retired or closed, and who have not performed any work in the last 3 months.',
            icon: 'fal fa-user-clock',

            entityType: EntityType.DRIVER,
          },
          {
            id: 'LOW_ENGAGEMENT_TRUCKS',
            longName: 'Low-engagement Trucks',
            description:
              'Trucks who have not been marked as retired or closed, and who have not performed any work in the last 3 months.',
            icon: 'fal fa-truck',

            entityType: EntityType.FLEET_ASSET,
          },
        ]
      : [
          {
            id: 'LOW_ENGAGEMENT_CLIENTS',
            longName: 'Low-engagement Clients',
            description:
              'Clients who have not been marked as closed, and who have not booked work with us in the last 3 months.',
            icon: 'fal fa-user-clock',
            entityType: EntityType.CLIENT,
          },
          {
            id: 'CLIENTS_REQUIRING_ATTENTION',
            longName: 'Clients Requiring Attention',
            description:
              'Clients that are in a PENDING state or have a Credit Status of SEE ACCOUNTS.',
            icon: 'fal fa-user-times',
            entityType: EntityType.CLIENT,
          },
          {
            id: 'ACTIVE_JOBS',
            longName: 'Active Jobs',
            description: 'Clients that have in-progress jobs.',
            icon: 'fal fa-truck-loading',
          },
        ];
  }

  get expirationTypesList(): ExpirationType[] {
    if (!this.selectedTabController) {
      return [];
    }

    const createExpirationType = (
      id: string,
      longName: string,
      description: string,
      icon: string,
      showEntityTypes: EntityType[],
      expirationType?: ExpiringDocumentType,
      tooltip?: string,
    ): ExpirationType => {
      return {
        id,
        longName,
        description,
        icon,
        showEntityTypes,
        expirationType,
        count: this.returnCountForEntityType(showEntityTypes, expirationType),
        isVisible:
          this.selectedTabController!.id === 'ALL' ||
          (id !== 'ALL_EXPIRATIONS' &&
            !!this.selectedTabController!.type &&
            showEntityTypes.includes(this.selectedTabController!.type)),
        tooltip: tooltip ? tooltip : '',
      };
    };

    return [
      createExpirationType(
        'ALL_EXPIRATIONS',
        'Summary (All)',
        'All current and upcoming items that require attention.',
        'fal fa-tasks',
        this.category === InvoiceEntityCategory.SUBCONTRACTOR
          ? [EntityType.FLEET_ASSET_OWNER, EntityType.DRIVER]
          : [EntityType.CLIENT],
      ),
      ...(this.category === InvoiceEntityCategory.SUBCONTRACTOR
        ? [
            createExpirationType(
              'EXPIRING_REGISTRATIONS',
              'Registration',
              'Vehicle Registrations that are expiring within the next 30 days, or have already expired.',
              'fal fa-truck-container',
              [EntityType.FLEET_ASSET],
              ExpiringDocumentType.REGISTRATION,
            ),
            createExpirationType(
              'EXPIRING_INSURANCES',
              'Insurance',
              'Insurances that are expiring within the next 30 days, or have already expired.',
              'fal fa-car-crash',
              [EntityType.FLEET_ASSET_OWNER, EntityType.FLEET_ASSET],
              ExpiringDocumentType.INSURANCE,
            ),
            createExpirationType(
              'EXPIRING_LICENCES',
              'Licences',
              'Licences that are due for renewal, or drivers that are missing required licences for an associated vehicle.',
              'fal fa-id-badge',
              [EntityType.DRIVER],
              ExpiringDocumentType.LICENCE,
              'Drivers will appear in this list if they have a Licence that is EXPIRED or expiring within the next 30 days. Generic or Retired drivers will not appear in this list.',
            ),
            createExpirationType(
              'EXPIRING_INDUCTIONS',
              'Inductions',
              'Inductions that are expiring, expired, or missing.',
              'fal fa-file-certificate',
              [EntityType.DRIVER],
              ExpiringDocumentType.INDUCTION,
              'Drivers will appear in this list if they have an Induction that is EXPIRED or expiring within the next 30 days. Generic or Retired drivers will not appear in this list.',
            ),
            createExpirationType(
              'EQUIPMENT_EXPIRATIONS',
              'Additional Equipment',
              'Additional Equipment that are due soon or overdue for a service, or are missing key details',
              'fal fa-forklift',
              [EntityType.FLEET_ASSET],
              ExpiringDocumentType.ADDITIONAL_EQUIPMENT,
              'Fleet Assets will appear in this list if they have either a Crane or Tail Gate service that is OVERDUE, due within the next 30, or missing key information. Please note that service reminders cannot be generated until all key details (Lifting Capacity, Manufacture date etc.) has been provided.',
            ),
            createExpirationType(
              'EXPIRING_RATE_CARDS',
              'Expiring Rate Cards',
              'Rate Cards that are expiring within the next 30 days, or have already expired.',
              'fal fa-money-check-alt',
              [EntityType.FLEET_ASSET],
              ExpiringDocumentType.SERVICE_RATE,
              `Fleet Asset Rate Cards will appear in this list if they are: EXPIRED or expiring within the next 30 days and do not have a follow-on Rate Card available for when the current Rate Card expires.`,
            ),
            createExpirationType(
              'EXPIRING_FUEL_LEVIES',
              'Expiring Fuel Levies',
              'Fuel Levies that are expiring within the next 30 days, or have already expired.',
              'fal fa-gas-pump',
              [EntityType.FLEET_ASSET],
              ExpiringDocumentType.FUEL_SURCHARGE,
              'Fleet Asset Fuel Surcharges will appear in this list if they are: EXPIRED or expiring within the next 30 days and do not have a follow-on rate available for when the current rate expires.',
            ),
            createExpirationType(
              'EXPIRING_DEFAULTS_CONFIGURATIONS',
              'Expiring Defaults Configurations',
              'Default rate configurations that are expiring within the next 30 days, or have already expired.',
              'fal fa-money-check-edit-alt',
              [EntityType.FLEET_ASSET],
              ExpiringDocumentType.DEFAULTS_CONFIGURATION,
              'Fleet Asset Default Configurations will appear in this list if they are: EXPIRED or expiring within the next 30 days, and do not have a follow-on Default Rate Card available. If expired Default Configurations are no longer needed, they should be deleted from the Fleet Asset.',
            ),
          ]
        : []),
      ...(this.category === InvoiceEntityCategory.CLIENT
        ? [
            createExpirationType(
              'EXPIRING_RATE_CARDS',
              'Expiring Rate Cards',
              'Rate Cards that are expiring within the next 30 days, or have already expired.',
              'fal fa-money-check-alt',
              [EntityType.CLIENT],
              ExpiringDocumentType.SERVICE_RATE,
              'Client Rate Cards will appear in this list if they are: EXPIRED or expiring within the next 30 days, do not have a follow-on Rate Card available, and are not configured to revert to the Division Rate Card.',
            ),
            createExpirationType(
              'EXPIRING_FUEL_LEVIES',
              'Expiring Fuel Levies',
              'Fuel Levies that are expiring within the next 30 days, or have already expired.',
              'fal fa-gas-pump',
              [EntityType.CLIENT],
              ExpiringDocumentType.FUEL_SURCHARGE,
              'Client Fuel Surcharges will appear in this list if they are: EXPIRED or expiring within the next 30 days, do not have a follow-on Rate Card available, and are not configured to revert to the Division Fuel Levy.',
            ),
            createExpirationType(
              'EXPIRING_DEFAULTS_CONFIGURATIONS',
              'Expiring Defaults Configurations',
              'Default rate configurations that are expiring within the next 30 days, or have already expired.',
              'fal fa-money-check-edit-alt',
              [EntityType.CLIENT],
              ExpiringDocumentType.DEFAULTS_CONFIGURATION,
              'Client Default Configurations will appear in this list if they are: EXPIRED or expiring within the next 30 days, and do not have a follow-on Default Rate Card available. If expired Default Configurations are no longer needed, they should be deleted from the Client.',
            ),
            createExpirationType(
              'EXPIRING_RATE_VARIATIONS',
              'Expiring Rate Variations',
              'Rate variations that are expiring within the next 30 days, or have already expired.',
              'fal fa-percentage',
              [EntityType.CLIENT],
              ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
              'Client Rate Variations will appear in this list if they are: EXPIRED or expiring within the next 30 days. These variations provide temporary adjustments to standard rates and should be reviewed when they expire.',
            ),
          ]
        : []),
    ];
  }

  // Options for menu above main content div, selecting what priority we're
  // filtering by. Defaults to 'All'
  public priorityOptions: Array<{
    id: number;
    name: string;
    class?: string;
    priority: number | null;
  }> = [
    {
      id: 1,
      name: 'All',
      priority: null,
    },
    {
      id: 2,
      name: '',
      class: 'error-type',
      priority: 1,
    },
    {
      id: 3,
      name: '',
      class: 'warn-type',
      priority: 2,
    },
  ];

  get selectedPriorityValue(): number | null {
    if (this.selectedPriorityOption) {
      const foundOption = this.priorityOptions.find(
        (i) => i.id === this.selectedPriorityOption,
      );
      return foundOption ? foundOption.priority : null;
    }
    return null;
  }
  // Returns the current view category. If mainContentController is contained in
  // expirationTypesList, then the view is EXPIRATIONS. Otherwise, it is
  // ACTIVITY.
  get viewType(): MainContentViewType {
    return this.expirationTypesList.some(
      (e) => e.id === this.mainContentController,
    )
      ? MainContentViewType.EXPIRATIONS
      : MainContentViewType.ACTIVITY;
  }

  /**
   * Returns the count of entities for the given entity types and optional expiration type.
   * @param entityTypes - The array of entity types.
   * @param expirationType - The optional expiration type.
   * @returns The count of entities.
   */
  public returnCountForEntityType(
    entityTypes: EntityType[],
    expirationType?: ExpiringDocumentType,
  ) {
    const groups = this.returnExpirationsSummaryListForEntityType(
      entityTypes,
      expirationType,
    );
    return this.returnSummaryCountForGroups(groups);
  }

  /**
   * Navigates to the entity page based on the provided entityId and entityType.
   * If an expirationType is specified, it will navigate to a specific tab
   * within the entity page.
   * @param entityId The ID of the entity.
   * @param entityType The type of the entity.
   * @param expirationType Optional. The type of the expiring document.
   */
  public viewEntityPage(
    entityId: string,
    entityType: EntityType,
    expirationType?: ExpiringDocumentType,
  ): void {
    if (!entityType || !entityId) {
      showNotification(GENERIC_ERROR_MESSAGE);
      return;
    }
    if (entityType === EntityType.CLIENT) {
      // For specific documentTypes, when we click on an entity we want to jump
      // to a particular tab immediately. For example, if documentType is
      // ExpiringDocumentType.SERVICE_RATE, we want to go straight to the
      // Service Rates tab in the Client Details.
      let clientDetailsViewType: string = '';
      switch (expirationType) {
        case ExpiringDocumentType.SERVICE_RATE:
          clientDetailsViewType = 'SERV';
          break;
        case ExpiringDocumentType.FUEL_SURCHARGE:
          clientDetailsViewType = 'FUEL';
          break;
        case ExpiringDocumentType.DEFAULTS_CONFIGURATION:
          clientDetailsViewType = 'DEF';
          break;
        case ExpiringDocumentType.SERVICE_RATE_VARIATIONS:
          clientDetailsViewType = 'SRVAR';
          break;
      }
      const clientSummary = this.clientDetailsStore.clientSummaryList.find(
        (c) => c.clientId === entityId,
      );
      if (!clientSummary) {
        return;
      }

      // Set the default view if one exists
      if (clientDetailsViewType) {
        this.clientDetailsStore.setSelectedClientDetailsView(
          clientDetailsViewType,
        );
      }
      this.clientDetailsStore.setSelectedClientId(entityId);
      this.$router.push({
        name: 'Client Details',
        params: {
          name: (clientSummary.tradingName
            ? clientSummary.tradingName
            : clientSummary.clientName
          )
            .toLowerCase()
            .replace(/ /g, '-'),
          id: entityId,
        },
      });
    } else if (entityType === EntityType.DRIVER) {
      // Set the default view if one exists
      let driverDetailsViewType = '';
      switch (expirationType) {
        case ExpiringDocumentType.LICENCE:
          driverDetailsViewType = 'LIC';
          break;
        case ExpiringDocumentType.INDUCTION:
          driverDetailsViewType = 'IND';
          break;
      }
      const driverDetailsStore = useDriverDetailsStore();
      if (driverDetailsViewType) {
        driverDetailsStore.setSelectedDriverDetailsView(driverDetailsViewType);
      }
      driverDetailsStore.setSelectedDriverDetailsId(entityId);
      const driver = driverDetailsStore.getDriverFromDriverId(entityId);
      this.$router.push({
        name: 'Driver',
        params: {
          name: driver?.displayName ?? '-',
          id: entityId,
        },
      });
    } else if (entityType === EntityType.FLEET_ASSET_OWNER) {
      // Set the default view if one exists
      let ownerViewType = '';
      switch (expirationType) {
        case ExpiringDocumentType.INSURANCE:
          ownerViewType = 'INS';
          break;
      }
      if (ownerViewType) {
        this.fleetAssetOwnerStore.setSelectedFleetAssetOwnerView(ownerViewType);
      }
      this.fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(entityId);
      const owner = this.fleetAssetOwnerStore.getOwnerFromOwnerId(entityId);
      this.$router.push({
        name: 'Owner',
        params: {
          name: owner && owner.name ? owner.name : '-',
          id: entityId,
        },
      });
    } else if (entityType === EntityType.FLEET_ASSET) {
      // For specific documentTypes, when we click on an entity we want to jump
      // to a particular tab immediately. For example, if documentType is
      // ExpiringDocumentType.SERVICE_RATE, we want to go straight to the
      // Service Rates tab in the Client Details.
      const fleetAsset =
        this.fleetAssetStore.getFleetAssetFromFleetAssetId(entityId);
      if (!fleetAsset) {
        showNotification(GENERIC_ERROR_MESSAGE);
        return;
      }
      const assetType = fleetAsset.fleetAssetTypeId === 1 ? 'Truck' : 'Trailer';
      let fleetAssetViewType: string = '';
      switch (expirationType) {
        case ExpiringDocumentType.SERVICE_RATE:
          if (assetType === 'Truck') {
            fleetAssetViewType = 'SERV';
          } else if (assetType === 'Trailer') {
            fleetAssetViewType = 'CON';
          }
          break;
        case ExpiringDocumentType.FUEL_SURCHARGE:
          if (assetType === 'Truck') {
            fleetAssetViewType = 'FUEL';
          }
          break;
        case ExpiringDocumentType.REGISTRATION:
          if (assetType === 'Truck') {
            fleetAssetViewType = 'COM';
          } else if (assetType === 'Trailer') {
            fleetAssetViewType = 'KEY';
          }
          break;
        case ExpiringDocumentType.ADDITIONAL_EQUIPMENT:
          fleetAssetViewType = 'AEQ';
          break;
        case ExpiringDocumentType.DEFAULTS_CONFIGURATION:
          fleetAssetViewType = 'DEF';
      }
      // Set the default view if one exists
      if (!assetType) {
        showNotification(GENERIC_ERROR_MESSAGE);
        return;
      }
      if (fleetAssetViewType) {
        this.fleetAssetStore.setSelectedFleetAssetView(fleetAssetViewType);
      }
      if (assetType === 'Truck') {
        this.fleetAssetStore.setSelectedFleetAssetId(entityId);
      } else if (assetType === 'Trailer') {
        this.fleetAssetStore.setSelectedFleetAssetTrailerId(entityId);
      }
      this.$router.push({
        name: assetType,
        params: {
          name: fleetAsset.csrAssignedId,
          id: entityId,
        },
      });
    }
  }

  /**
   * Returns the expiration summary list for the given entity types and expiration type. Returns the expiration summaries grouped by entity type and entityId.
   * @param entityTypes - An array of entity types.
   * @param expirationType - Optional. The expiration document type.
   * @returns An array of RateExpirationSummaryGroup objects representing the expiration summary list.
   */
  public returnExpirationsSummaryListForEntityType(
    entityTypes: EntityType[],
    expirationType?: ExpiringDocumentType,
  ): RateExpirationSummaryGroup[] {
    const getSummaries = (
      entityType: EntityType,
      expType?: ExpiringDocumentType,
    ): RateExpirationSummaryGroup[] => {
      switch (entityType) {
        // For owners we should also find the related fleet assets and drivers,
        // such that we can show them in the tree view to visualise relationship
        case EntityType.FLEET_ASSET_OWNER:
          if (!expType) {
            return this.fleetAssetOwnerExpirationSummaries;
          } else {
            return this.fleetAssetOwnerExpirationSummaries
              .map((e) => {
                return {
                  ...e,
                  summaries: e.summaries.filter(
                    (es) => es.expirationType === expType,
                  ),
                  relatedGroups: e.relatedGroups
                    ? e.relatedGroups
                        .map((rg) => {
                          return {
                            ...rg,
                            summaries: rg.summaries.filter(
                              (es) => es.expirationType === expType,
                            ),
                          };
                        })
                        .filter((rg) => rg.summaries.length > 0)
                    : [],
                };
              })
              .filter(
                (e) =>
                  e.summaries.length > 0 ||
                  e.relatedGroups.some((rg) => rg.summaries.length > 0),
              );
          }

        case EntityType.DRIVER:
          return !expType
            ? this.driverExpirationSummaries
            : this.driverExpirationSummaries
                .map((e) => {
                  return {
                    ...e,
                    summaries: e.summaries.filter(
                      (es) => es.expirationType === expType,
                    ),
                  };
                })
                .filter((e) => e.summaries.length > 0);
        case EntityType.FLEET_ASSET:
          return !expType
            ? this.fleetAssetExpirationSummaries
            : this.fleetAssetExpirationSummaries
                .map((e) => {
                  return {
                    ...e,
                    summaries: e.summaries.filter(
                      (es) => es.expirationType === expType,
                    ),
                  };
                })
                .filter((e) => e.summaries.length > 0);
        case EntityType.CLIENT:
          return !expType
            ? this.clientExpirationSummaries
            : this.clientExpirationSummaries
                .map((e) => {
                  return {
                    ...e,
                    summaries: e.summaries.filter(
                      (es) => es.expirationType === expType,
                    ),
                  };
                })
                .filter((e) => e.summaries.length > 0);
        default:
          return [];
      }
    };
    const resultList: RateExpirationSummaryGroup[] = [];
    // Keep track of visited entities to avoid duplicates
    const visitedEntities = new Set();
    // For each entity type, get the summaries and add to the resultList
    entityTypes.forEach((entityType) => {
      const summaries = getSummaries(entityType, expirationType);
      // For each summary, add to the resultList if it hasn't already been added
      summaries.forEach((summary) => {
        // Construct a unique id that is unique across all entity types
        const uid = `${summary.entityType}-${summary.entityId}`;
        if (!visitedEntities.has(uid)) {
          // Add to visited entities and push to resultList
          visitedEntities.add(uid);
          resultList.push(summary);
          // Not we check the related groups, so that we can mark them as visited so they only appear once
          if (summary.relatedGroups) {
            // Iterate over related groups and add to set
            summary.relatedGroups.forEach((rg) => {
              const rgUid = `${rg.entityType}-${rg.entityId}`;
              if (!visitedEntities.has(rgUid)) {
                visitedEntities.add(rgUid);
              }
            });
          }
        }
      });
    });

    return resultList;
  }

  /**
   * Sets the selected priority option and scrolls to the top of the main
   * content div.
   * @param id - The ID of the priority option to be selected.
   */
  public setSelectedPriority(id: number): void {
    if (this.selectedPriorityOption === id) {
      return;
    }
    this.selectedPriorityOption = id;
    // Scroll main-content div back to top
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.scrollTop = 0;
    }
  }

  /**
   * Returns the list of rate expiration summaries for the selected menu items.
   * Displayed in the center section as a list of results.
   * @returns {RateExpirationSummaryGroup[]} The list of rate expiration
   * summaries.
   */
  get expirationListForCurrentSelection(): RateExpirationSummaryGroup[] {
    if (!this.selectedExpirationItem) {
      return [];
    }
    return (
      this.returnExpirationsSummaryListForEntityType(
        this.selectedExpirationItem.showEntityTypes,
        this.selectedExpirationItem.expirationType,
      )
        // Filter out summaries that don't match the selected priority. If no
        // priority is selected then return all of them
        .map((e) => {
          return {
            ...e,
            summaries: e.summaries.filter(
              (s) =>
                this.selectedPriorityValue === null ||
                s.priority === this.selectedPriorityValue,
            ),
            relatedGroups: e.relatedGroups
              ? e.relatedGroups
                  .map((rg) => {
                    return {
                      ...rg,
                      summaries: rg.summaries.filter(
                        (s) =>
                          this.selectedPriorityValue === null ||
                          s.priority === this.selectedPriorityValue,
                      ),
                    };
                  })
                  .filter((rg) => rg.summaries.length > 0)
              : [],
          };
        })
        .filter(
          (e) =>
            e.summaries.length > 0 ||
            (e.relatedGroups &&
              e.relatedGroups.some((rg) => rg.summaries.length > 0)),
        )
    );
  }

  /**
   * Sorts RateExpirationSummaryGroup objects by name, ignoring any special
   * characters such as brackets.
   * @param a - The first RateExpirationSummaryGroup object.
   * @param b - The second RateExpirationSummaryGroup object.
   * @returns A negative number if a should be sorted before b, a positive
   * number if a should be sorted after b, or 0 if they are equal.
   */
  public sortByName(
    a: RateExpirationSummaryGroup,
    b: RateExpirationSummaryGroup,
  ) {
    const aName = a.name.replace(/[^a-zA-Z0-9]/g, '');
    const bName = b.name.replace(/[^a-zA-Z0-9]/g, '');
    return aName.localeCompare(bName);
  }

  /**
   * Returns a readable entity type name based on the provided EntityType.
   * @param entityType - The EntityType to convert to a readable string.
   * @returns The readable entity type as a string.
   */
  public returnReadableEntityType(entityType: EntityType): string {
    switch (entityType) {
      case EntityType.CLIENT:
        return 'Client';
      case EntityType.FLEET_ASSET_OWNER:
        return 'Owner';
      case EntityType.FLEET_ASSET:
        return 'Fleet Asset';
      case EntityType.DRIVER:
        return 'Driver';
      default:
        return '';
    }
  }

  /**
   * Returns a readable entity type icon based on the provided EntityType.
   * @param entityType - The EntityType to convert to a readable string.
   * @returns The readable entity type as a string.
   */
  public getIconClass(entityType: EntityType): string {
    switch (entityType) {
      case EntityType.CLIENT:
        return 'fas fa-briefcase';
      case EntityType.FLEET_ASSET_OWNER:
        return 'fas fa-user';
      case EntityType.FLEET_ASSET:
        return 'fas fa-truck-moving';
      case EntityType.DRIVER:
        return 'fas fa-steering-wheel';
      default:
        return '';
    }
  }
  /**
   * Handles emit from child components, which fetch or emit data to be
   * displayed within this component. Sets the data to a local variable, which is
   * read from to display in html
   * @param payload contains table data to be displayed
   */
  public setActivityTableData(payload: {
    entityType: EntityType;
    tableData: DashboardTableData[];
  }) {
    this.activityTableData = payload.tableData;
  }
  /**
   * Handles emit from child DashboardActiveJobCounter component, which emits
   * data related to current active jobs. Sets the data to a local variable,
   * which is read from to display in html
   * @param payload contains table data to be displayed
   */
  public setActivityJobTableData(data: DashboardJobTableData[]) {
    this.activityJobTableData = data;
  }
  /**
   * Initialise the data for the component
   */
  public beforeMount() {
    this.setCategorySpecificData();
  }

  /**
   * Calculates the total number of summaries in the given list of rate
   * expiration summary groups.
   * @param expirationGroups - The list of rate expiration summary groups.
   * @returns The total number of summaries.
   */
  public returnSummaryCountForGroups(
    expirationGroups: RateExpirationSummaryGroup[],
  ): ExpirationCount {
    const total = expirationGroups.reduce(
      (acc, curr) =>
        acc +
        (curr.summaries.length +
          (curr.relatedGroups
            ? curr.relatedGroups.reduce((a, c) => a + c.summaries.length, 0)
            : 0)),
      0,
    );
    // Return counts of summaries and relatedGroups' summaries where RateExpirationSummary.priority === 1
    const urgent = expirationGroups.reduce(
      (acc, curr) =>
        acc +
        (curr.summaries.filter((s) => s.priority === 1).length +
          (curr.relatedGroups
            ? curr.relatedGroups.reduce(
                (a, c) =>
                  a +
                  c.summaries.filter((s) => s.priority === 1).length +
                  (c.relatedGroups
                    ? c.relatedGroups.reduce(
                        (a2, c2) =>
                          a2 +
                          c2.summaries.filter((s) => s.priority === 1).length,
                        0,
                      )
                    : 0),
                0,
              )
            : 0)),
      0,
    );
    // Return counts of summaries and relatedGroups' summaries where RateExpirationSummary.priority === 2
    const soon = expirationGroups.reduce(
      (acc, curr) =>
        acc +
        (curr.summaries.filter((s) => s.priority === 2).length +
          (curr.relatedGroups
            ? curr.relatedGroups.reduce(
                (a, c) =>
                  a +
                  c.summaries.filter((s) => s.priority === 2).length +
                  (c.relatedGroups
                    ? c.relatedGroups.reduce(
                        (a2, c2) =>
                          a2 +
                          c2.summaries.filter((s) => s.priority === 2).length,
                        0,
                      )
                    : 0),
                0,
              )
            : 0)),
      0,
    );

    return { total, urgent, soon };
  }

  /**
   * Returns the count of results based on the current view type.
   * - If the view type is EXPIRATIONS, it returns the total count of all
   *   summaries in expirationListForCurrentSelection plus the count of
   *   relatedGroups' summaries.
   * - If the view type is ACTIVITY and the mainContentController is
   *   ACTIVE_JOBS, it returns the length of activityJobTableData.
   * - If the view type is ACTIVITY and the mainContentController is not
   *   ACTIVE_JOBS, it returns the length of activityTableData. Otherwise, it
   *   returns 0.
   */
  get resultCount() {
    if (this.viewType === MainContentViewType.EXPIRATIONS) {
      // Return the total count of all summaries in
      // expirationListForCurrentSelection + the count relatedGroups' summaries
      return this.returnSummaryCountForGroups(
        this.expirationListForCurrentSelection,
      ).total;
    } else if (this.viewType === MainContentViewType.ACTIVITY) {
      return this.mainContentController === 'ACTIVE_JOBS'
        ? this.activityJobTableData.length
        : this.activityTableData.length;
    } else {
      return 0;
    }
  }

  /**
   *  Gets and set subcontractorDashboardState or clientDashboardState in the
   *  store, depending on the category. Used so we can maintain our position
   *  when we go to a different page and come back.
   *
   */
  get navigationState(): AdminDashboardNavigationState {
    if (this.category === InvoiceEntityCategory.SUBCONTRACTOR) {
      return this.appNavigationStore.subcontractorDashboardState;
    } else {
      return this.appNavigationStore.clientDashboardState;
    }
  }
  set navigationState(value: AdminDashboardNavigationState) {
    this.commitNavigationStateToStore(value);
  }

  /**
   * Current menu button selections for the large buttons left side of
   * the UI, and also on the right side of the main content.
   */
  get mainContentController(): string {
    return this.navigationState.mainContentId;
  }
  set mainContentController(value: string) {
    if (value !== this.mainContentController) {
      this.activityTableData = [];
      this.activityJobTableData = [];
      this.selectedPriorityOption = 1;
      // Scroll main-content div back to top
      const mainContent = document.getElementById('main-content');
      if (mainContent) {
        mainContent.scrollTop = 0;
      }
    }
    // Set the current selection to store
    this.commitNavigationStateToStore({
      ...this.navigationState,
      mainContentId: value,
    });
  }

  /**
   * Commits the navigation state to the store. Uses a different mutation method
   * depending on if we're in a CLIENT or SUBCONTRACTOR instance of this
   * component, such that states can be maintained separately.
   * @param value - The navigation state to be committed.
   */
  public commitNavigationStateToStore(value: AdminDashboardNavigationState) {
    // Set the current selection to store
    if (this.category === InvoiceEntityCategory.SUBCONTRACTOR) {
      this.appNavigationStore.setSubcontractorDashboardState(value);
    } else {
      this.appNavigationStore.setClientDashboardState(value);
    }
  }

  /**
   * Modelled to tabs in HTML, which contain a list of entity types. Used to
   * filter the expiration summary type buttons on the left side.
   */
  get selectedTabController(): TabItem | undefined {
    return this.tabItems.find((t) => t.id === this.navigationState.tabId);
  }
  set selectedTabController(value: TabItem | undefined) {
    if (!value) {
      return;
    }
    this.commitNavigationStateToStore({
      ...this.navigationState,
      tabId: value.id,
    });

    // Get first expirationTypesList item where isVisible is true
    const expirationType = this.expirationTypesList.find((e) => e.isVisible);
    if (expirationType) {
      this.mainContentController = expirationType.id;
    }
  }
  /**
   * Returns the selected expiration item based on the mainContentController
   */
  get selectedExpirationItem(): ExpirationType | undefined {
    return this.expirationTypesList.find(
      (i) => i.id === this.mainContentController,
    );
  }
  get selectedActivityItem(): ActivityItemType | undefined {
    return this.entityActivityList.find(
      (i) => i.id === this.mainContentController,
    );
  }

  /**
   * For the provided epoch time in ms, uses moment to return true if it is
   * within 7 days from now
   */
  public isWithin7Days(epochTime?: number): boolean {
    if (!epochTime) {
      return false;
    }
    return (
      moment()
        .tz(useCompanyDetailsStore().userLocale)
        .startOf('day')
        .add(7, 'days')
        .valueOf() >= epochTime
    );
  }
  /**
   * For the provided validToDate, return what class should be applied to the
   * html element that contains it.
   * @param epochTime the validToDate
   * @returns
   */
  public returnValidToDateClass(epochTime?: number) {
    if (!epochTime) {
      return 'error-type';
    } else {
      if (epochTime <= returnStartOfDayFromEpoch()) {
        return 'error-type';
      } else {
        if (this.isWithin7Days(epochTime)) {
          return 'warn-type';
        } else {
          return '';
        }
      }
    }
  }
  public setCategorySpecificData() {
    if (this.category === InvoiceEntityCategory.SUBCONTRACTOR) {
      this.tabItems = [
        {
          id: 'ALL',
          name: 'All',
        },
        {
          id: 'OWNERS',
          name: 'Owners',
          type: EntityType.FLEET_ASSET_OWNER,
        },
        {
          id: 'DRIVERS',
          name: 'Drivers',
          type: EntityType.DRIVER,
        },
        {
          id: 'TRUCKS',
          name: 'Trucks',
          type: EntityType.FLEET_ASSET,
        },
      ];
      // Get the expiration summaries for each entity type and set to local variables
      this.fleetAssetExpirationSummaries =
        this.fleetAssetStore.getAllFleetAssetList
          .map((fa) => {
            return {
              id: fa.fleetAssetId,
              name: `${fa.csrAssignedId} - ${fa.registrationNumber} (${
                fa.fleetAssetTypeId === 1 ? 'Truck' : 'Trailer'
              })`,
              summaries: combineExpirationSummaries({
                type: EntityType.FLEET_ASSET,
                expirationSummary: fa.expirationSummaries,
                entityId: fa.fleetAssetId,
              }),
              entityType: EntityType.FLEET_ASSET,
              entityId: fa.fleetAssetId,
            };
          })
          .filter((fa) => fa.summaries.length > 0)
          .sort((a, b) => this.sortByName(a, b));
      // Get the list of unique ownerIds from the fleetAssetExpirationSummaries.
      const ownerIdsFromFleetAssets = new Set(
        this.fleetAssetExpirationSummaries
          .map((es) =>
            this.fleetAssetStore.getFleetAssetFromFleetAssetId(es.entityId),
          )
          .filter((fa) => !!fa && fa.fleetAssetOwnerId)
          .map((fa) => fa!.fleetAssetOwnerId),
      );
      this.driverExpirationSummaries = useDriverDetailsStore()
        .getDriverList.map((d) => {
          return {
            id: d.driverId,
            name: d.displayName,
            summaries: combineExpirationSummaries({
              type: EntityType.DRIVER,
              expirationSummary: d.expirationSummaries,
              entityId: d.driverId,
            }),
            entityType: EntityType.DRIVER,
            entityId: d.driverId,
          };
        })
        .filter((d) => d.summaries.length > 0)
        .sort((a, b) => this.sortByName(a, b));
      // Get unique driverIds so we can compare to owner.associatedDrivers
      const driverIdsFromDrivers = new Set(
        this.driverExpirationSummaries.map((es) => es.entityId),
      );
      // Get summaries for owners, but also include owners who are associated
      // with a fleet asset that has an expiration summary, or owners who are
      // associated with a driver that has an expiration summary
      this.fleetAssetOwnerExpirationSummaries = useFleetAssetOwnerStore()
        .getOwnerList.map((o) => {
          return {
            id: o.ownerId,
            name: o.name,
            summaries: combineExpirationSummaries({
              type: EntityType.FLEET_ASSET_OWNER,
              expirationSummary: o.expirationSummaries,
              entityId: o.ownerId,
            }),
            entityType: EntityType.FLEET_ASSET_OWNER,
            entityId: o.ownerId,
            relatedGroups: o.relatedExpirationSummaries,
          };
        })
        .filter(
          (o) =>
            o.summaries.length > 0 ||
            // Has associated vehicle visible
            ownerIdsFromFleetAssets.has(o.entityId) ||
            // Has associated driver visible
            (this.fleetAssetOwnerStore.getOwnerFromOwnerId(o.entityId) &&
              useFleetAssetOwnerStore()
                .getOwnerFromOwnerId(o.entityId)!
                .associatedDrivers.some((d) => driverIdsFromDrivers.has(d))),
        )
        .sort((a, b) => this.sortByName(a, b));
    } else if (this.category === InvoiceEntityCategory.CLIENT) {
      this.tabItems = [
        {
          id: 'ALL',
          name: 'Client',
        },
      ];
      this.clientExpirationSummaries = useClientDetailsStore()
        .clientSummaryList.map((c) => {
          return {
            id: c.clientId,
            name: c.clientDisplayName,
            summaries: combineExpirationSummaries({
              type: EntityType.CLIENT,
              expirationSummary: c.rateExpirationSummaries,
              entityId: c.clientId,
            }),
            entityType: EntityType.CLIENT,
            entityId: c.clientId,
          };
        })
        .filter((d) => d.summaries.length > 0)
        .sort((a, b) => this.sortByName(a, b));
    }
  }
  public mounted() {
    const foundIndex = this.entityActivityList.findIndex(
      (i) => i.id === this.mainContentController,
    );
    // If the component mounts and the mainContentController is an
    // entityActivityList item, we need to call the emitTableData method in the
    // child component to initialise the data
    if (foundIndex !== -1) {
      try {
        const activityItem = this.$refs.activityItem as Vue[];
        if (activityItem[foundIndex]) {
          (activityItem[foundIndex] as any).emitTableData();
        }
      } catch (e) {
        console.error(e);
      }
    }

    // Ensure rate variation expiration summaries are up to date when dashboard loads
    this.rateVariationExpirationSummariesAreLoaded();
  }

  /**
   * Ensures that rate variation expiration summaries are loaded and up to date
   * This addresses the issue where dashboard shows stale data until rate variations page is visited
   */
  private async rateVariationExpirationSummariesAreLoaded(): Promise<void> {
    await this.serviceRateVariationsStore.getDivisionRateVariationsByStatus(
      ClientServiceRateVariationsStatus.ALL,
    );
    this.setCategorySpecificData();
  }
}
